@echo off
echo [+] 清理编译文件和临时文件...

:: 清理编译生成的文件
if exist "bin" (
    echo [+] 删除 bin 目录...
    rmdir /s /q bin
)

if exist "obj" (
    echo [+] 删除 obj 目录...
    rmdir /s /q obj
)

:: 清理临时文件
del *.obj >nul 2>&1
del *.exp >nul 2>&1
del *.lib >nul 2>&1
del *.pdb >nul 2>&1
del *.ilk >nul 2>&1

echo [+] 清理完成！

:: 结束相关进程
echo [+] 结束测试进程...
taskkill /f /im TestTarget.exe >nul 2>&1
taskkill /f /im Controller.exe >nul 2>&1
taskkill /f /im Injector.exe >nul 2>&1

echo [+] 所有清理工作完成！
pause