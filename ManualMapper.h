#pragma once
#include <Windows.h>
#include <iostream>
#include <fstream>
#include <vector>

// 修复编译问题的宏定义
#ifndef RELOC_FLAG
#define RELOC_FLAG(RelInfo) ((RelInfo >> 0x0C) == IMAGE_REL_BASED_HIGHLOW)
#endif

// 手动映射相关结构体
struct MANUAL_MAPPING_DATA
{
    LPVOID ImageBase;
    HMODULE(WINAPI* fnLoadLibraryA)(LPCSTR);
    FARPROC(WINAPI* fnGetProcAddress)(HMODULE, LPCSTR);
    BOOL(WINAPI* fnDllMain)(HMODULE, DWORD, LPVOID);
};

class ManualMapper
{
public:
    static bool InjectDLL(HANDLE hProcess, const std::string& dllPath);
    static bool MapDLL(HANDLE hProcess, BYTE* pSrcData, SIZE_T fileSize);
    
private:
    static void __stdcall Shellcode(MANUAL_MAPPING_DATA* pData);
    static bool ResolveImports(BYTE* pBase, IMAGE_IMPORT_DESCRIPTOR* pImportDesc, MANUAL_MAPPING_DATA* pData);
    static bool ResolveRelocations(BYTE* pBase, IMAGE_BASE_RELOCATION* pRelocData, DWORD_PTR deltaImageBase);
    static std::vector<BYTE> ReadFileToMemory(const std::string& filePath);
};

// 通信协议定义
#define PIPE_NAME L"\\\\.\\pipe\\MemoryToolPipe"
#define BUFFER_SIZE 4096

enum CommandType
{
    CMD_READ_MEMORY = 1,
    CMD_WRITE_MEMORY = 2,
    CMD_PROTECT_MEMORY = 3,
    CMD_ALLOCATE_MEMORY = 4,
    CMD_FREE_MEMORY = 5
};

struct Command
{
    CommandType type;
    DWORD_PTR address;
    SIZE_T size;
    DWORD protection;
    BYTE data[BUFFER_SIZE - sizeof(CommandType) - sizeof(DWORD_PTR) - sizeof(SIZE_T) - sizeof(DWORD)];
};

struct Response
{
    BOOL success;
    DWORD errorCode;
    SIZE_T dataSize;
    BYTE data[BUFFER_SIZE - sizeof(BOOL) - sizeof(DWORD) - sizeof(SIZE_T)];
};