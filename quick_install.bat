@echo off
echo ========================================
echo    快速安装编译环境
echo ========================================
echo.

echo 检测到您的系统中没有C++编译器
echo.
echo 请选择安装方案:
echo.
echo 1. 打开Visual Studio Community下载页面 (推荐)
echo 2. 打开MinGW-w64下载页面
echo 3. 查看详细安装指南
echo 4. 退出
echo.
set /p choice="请输入选择 (1-4): "

if "%choice%"=="1" (
    echo [+] 正在打开Visual Studio Community下载页面...
    start https://visualstudio.microsoft.com/zh-hans/vs/community/
    echo.
    echo 安装提示:
    echo 1. 下载并运行安装程序
    echo 2. 选择"使用C++的桌面开发"工作负载
    echo 3. 确保包含MSVC编译器和Windows SDK
    echo 4. 安装完成后使用"开发人员命令提示符"
    echo 5. 运行 build.bat 编译项目
) else if "%choice%"=="2" (
    echo [+] 正在打开MinGW-w64下载页面...
    start https://www.mingw-w64.org/downloads/
    echo.
    echo 推荐使用MSYS2: https://www.msys2.org/
    start https://www.msys2.org/
    echo.
    echo 安装提示:
    echo 1. 下载并安装MSYS2
    echo 2. 在MSYS2终端中运行: pacman -S mingw-w64-x86_64-gcc
    echo 3. 将 C:\msys64\mingw64\bin 添加到系统PATH
    echo 4. 运行 build_mingw.bat 编译项目
) else if "%choice%"=="3" (
    echo [+] 正在打开安装指南...
    start notepad install_guide.md
) else if "%choice%"=="4" (
    echo [+] 退出
    exit /b 0
) else (
    echo [-] 无效选择
)

echo.
pause