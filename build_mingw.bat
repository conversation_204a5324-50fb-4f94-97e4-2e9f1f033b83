@echo off
echo [+] 使用MinGW编译无模块DLL注入工具
echo ================================

:: 检查MinGW环境
where gcc >nul 2>&1
if %errorlevel% neq 0 (
    echo [-] 未找到MinGW编译器
    echo [!] 请安装MinGW-w64或TDM-GCC
    echo [!] 下载地址: https://www.mingw-w64.org/downloads/
    pause
    exit /b 1
)

:: 创建输出目录
if not exist "bin" mkdir bin

echo [+] 编译注入器...
g++ -std=c++11 -O2 -static -o bin/Injector.exe Injector.cpp ManualMapper.cpp -lkernel32 -luser32 -ladvapi32
if %errorlevel% neq 0 (
    echo [-] 注入器编译失败
    pause
    exit /b 1
)

echo [+] 编译被注入DLL...
g++ -std=c++11 -O2 -shared -static -o bin/Payload.dll Payload.cpp -lkernel32 -luser32 -Wl,--enable-stdcall-fixup
if %errorlevel% neq 0 (
    echo [-] DLL编译失败
    pause
    exit /b 1
)

echo [+] 编译测试目标...
g++ -std=c++11 -O2 -static -o bin/TestTarget.exe test_target.cpp -lkernel32 -luser32
if %errorlevel% neq 0 (
    echo [-] 测试目标编译失败
    pause
    exit /b 1
)

echo [+] 编译控制器...
g++ -std=c++11 -O2 -static -o bin/Controller.exe Controller.cpp -lkernel32 -luser32
if %errorlevel% neq 0 (
    echo [-] 控制器编译失败
    pause
    exit /b 1
)

echo [+] 编译完成！
echo [+] 输出文件位于 bin\ 目录
echo.
echo 使用方法:
echo   1. bin\Injector.exe ^<进程名^> bin\Payload.dll
echo   2. bin\Controller.exe
echo.
pause