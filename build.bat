@echo off
echo [+] 编译无模块DLL注入工具
echo ================================

:: 检查Visual Studio环境
where cl >nul 2>&1
if %errorlevel% neq 0 (
    echo [-] 未找到Visual Studio编译器
    echo [!] 请运行 "Developer Command Prompt for VS" 或设置环境变量
    pause
    exit /b 1
)

:: 创建输出目录
if not exist "bin" mkdir bin
if not exist "obj" mkdir obj

echo [+] 编译注入器...
cl /EHsc /O2 /MT /Fe:bin\Injector.exe Injector.cpp ManualMapper.cpp /link kernel32.lib user32.lib advapi32.lib /SUBSYSTEM:CONSOLE
if %errorlevel% neq 0 (
    echo [-] 注入器编译失败
    pause
    exit /b 1
)

echo [+] 编译被注入DLL...
cl /EHsc /O2 /MT /LD /Fe:bin\Payload.dll Payload.cpp /link kernel32.lib user32.lib /DEF:Payload.def
if %errorlevel% neq 0 (
    echo [-] DLL编译失败
    pause
    exit /b 1
)

echo [+] 编译测试目标...
cl /EHsc /O2 /MT /Fe:bin\TestTarget.exe test_target.cpp /link kernel32.lib user32.lib /SUBSYSTEM:CONSOLE
if %errorlevel% neq 0 (
    echo [-] 测试目标编译失败
    pause
    exit /b 1
)

echo [+] 编译控制器...
cl /EHsc /O2 /MT /Fe:bin\Controller.exe Controller.cpp /link kernel32.lib user32.lib /SUBSYSTEM:CONSOLE
if %errorlevel% neq 0 (
    echo [-] 控制器编译失败
    pause
    exit /b 1
)

:: 清理临时文件
del *.obj >nul 2>&1
del *.exp >nul 2>&1
del *.lib >nul 2>&1

echo [+] 编译完成！
echo [+] 输出文件位于 bin\ 目录
echo.
echo 使用方法:
echo   1. bin\Injector.exe ^<进程名^> bin\Payload.dll
echo   2. bin\Controller.exe
echo.
pause