# 无模块DLL注入工具

这是一个完整的无模块DLL注入工具，可以将DLL注入到目标进程中而不在PEB中留下模块记录，并提供远程内存操作功能。

## 功能特性

- **无模块注入**: 使用手动映射技术，避免在PEB中留下痕迹
- **内存操作**: 支持读取、写入、分配、释放目标进程内存
- **进程间通信**: 通过命名管道与注入的DLL通信
- **十六进制查看器**: 提供内存数据的十六进制显示
- **权限提升**: 自动获取调试权限

## 项目结构

```
├── ManualMapper.h          # 手动映射核心头文件
├── ManualMapper.cpp        # 手动映射实现
├── Injector.cpp           # 注入器主程序
├── Payload.cpp            # 被注入的DLL
├── Payload.def            # DLL导出定义
├── Controller.cpp         # 内存操作控制器
├── test_target.cpp        # 测试目标程序
├── build.bat             # 编译脚本
└── README.md             # 说明文档
```

## 编译方法

1. 打开 "Developer Command Prompt for Visual Studio"
2. 进入项目目录
3. 运行编译脚本：
   ```cmd
   build.bat
   ```

编译完成后，在 `bin/` 目录下会生成以下文件：
- `Injector.exe` - 注入器
- `Payload.dll` - 被注入的DLL
- `Controller.exe` - 内存操作控制器
- `TestTarget.exe` - 测试目标程序

## 使用方法

### 1. 准备测试环境

首先运行测试目标程序：
```cmd
bin\TestTarget.exe
```

### 2. 执行DLL注入

在新的命令行窗口中运行注入器：
```cmd
bin\Injector.exe TestTarget.exe bin\Payload.dll
```

或者使用进程ID：
```cmd
bin\Injector.exe 1234 bin\Payload.dll
```

### 3. 使用控制器操作内存

注入成功后，运行控制器：
```cmd
bin\Controller.exe
```

控制器提供以下功能：
1. **读取内存** - 从指定地址读取数据
2. **写入内存** - 向指定地址写入数据
3. **分配内存** - 在目标进程中分配内存
4. **释放内存** - 释放已分配的内存
5. **十六进制转储** - 以十六进制格式显示内存内容

## 技术原理

### 无模块注入技术

1. **手动映射**: 不使用LoadLibrary，而是手动解析PE文件并映射到内存
2. **重定位处理**: 手动处理基址重定位
3. **导入表解析**: 手动解析并填充导入表
4. **避免PEB记录**: 由于不使用系统API加载，不会在PEB中留下模块记录

### 进程间通信

使用命名管道实现注入器与被注入DLL之间的通信：
- 管道名称: `\\.\pipe\MemoryToolPipe`
- 通信协议: 自定义二进制协议
- 支持的命令: 读写内存、分配释放内存、修改内存保护

### 内存操作

支持以下内存操作：
- `ReadProcessMemory` - 读取内存
- `WriteProcessMemory` - 写入内存  
- `VirtualAlloc` - 分配内存
- `VirtualFree` - 释放内存
- `VirtualProtect` - 修改内存保护

## 安全注意事项

⚠️ **重要提醒**: 此工具仅用于学习和研究目的

- 需要管理员权限运行
- 可能被杀毒软件误报
- 请勿用于恶意目的
- 仅在受控环境中测试

## 故障排除

### 常见问题

1. **编译失败**
   - 确保已安装Visual Studio
   - 使用Developer Command Prompt
   - 检查Windows SDK是否安装

2. **注入失败**
   - 确保以管理员身份运行
   - 检查目标进程是否存在
   - 确认DLL文件路径正确

3. **连接失败**
   - 确保DLL已成功注入
   - 检查防火墙设置
   - 重新启动目标程序

### 调试模式

在Debug模式下编译DLL会显示额外的调试信息：
```cmd
cl /EHsc /O2 /MT /LD /D_DEBUG /Fe:bin\Payload.dll Payload.cpp
```

## 许可证

此项目仅供教育和研究使用。使用者需自行承担使用风险。

## 贡献

欢迎提交问题报告和改进建议。