#include <Windows.h>
#include <iostream>
#include <string>

// 测试目标程序 - 用于演示注入功能
class TestTarget
{
private:
    int m_secretValue;
    std::string m_secretString;
    
public:
    TestTarget() : m_secretValue(12345), m_secretString("Hello World!") {}
    
    void PrintInfo()
    {
        std::cout << "进程ID: " << GetCurrentProcessId() << std::endl;
        std::cout << "秘密数值: " << m_secretValue << " (地址: 0x" 
                  << std::hex << &m_secretValue << ")" << std::endl;
        std::cout << "秘密字符串: " << m_secretString << " (地址: 0x" 
                  << std::hex << m_secretString.c_str() << ")" << std::endl;
        std::cout << "对象地址: 0x" << std::hex << this << std::endl;
    }
    
    void UpdateValue(int newValue)
    {
        m_secretValue = newValue;
        std::cout << "数值已更新为: " << newValue << std::endl;
    }
    
    int GetValue() const { return m_secretValue; }
};

int main()
{
    std::cout << "=== 测试目标程序 ===" << std::endl;
    std::cout << "此程序用于测试DLL注入功能" << std::endl;
    std::cout << "========================" << std::endl;
    
    TestTarget target;
    
    std::cout << "\n初始状态:" << std::endl;
    target.PrintInfo();
    
    std::cout << "\n程序正在运行，可以进行注入测试..." << std::endl;
    std::cout << "按 'q' 退出，按其他键刷新显示" << std::endl;
    
    char input;
    while (true)
    {
        std::cin >> input;
        if (input == 'q' || input == 'Q')
            break;
            
        std::cout << "\n当前状态:" << std::endl;
        target.PrintInfo();
        std::cout << "\n继续运行中..." << std::endl;
    }
    
    return 0;
}