# 编译环境安装指南

由于您的系统中没有找到C++编译器，请按照以下步骤安装编译环境：

## 方案一：Visual Studio Community (推荐)

1. **下载Visual Studio Community 2022**
   - 访问：https://visualstudio.microsoft.com/zh-hans/vs/community/
   - 点击"免费下载"

2. **安装时选择工作负载**
   - 勾选"使用C++的桌面开发"
   - 确保包含"MSVC v143编译器工具集"
   - 确保包含"Windows 10/11 SDK"

3. **安装完成后**
   - 打开"开发人员命令提示符"
   - 或在开始菜单搜索"Developer Command Prompt"

4. **编译项目**
   ```cmd
   cd 项目目录
   build.bat
   ```

## 方案二：MinGW-w64

1. **下载MinGW-w64**
   - 访问：https://www.mingw-w64.org/downloads/
   - 推荐使用MSYS2：https://www.msys2.org/

2. **安装MSYS2**
   - 下载并运行安装程序
   - 安装完成后打开MSYS2终端

3. **安装编译器**
   ```bash
   pacman -S mingw-w64-x86_64-gcc
   pacman -S mingw-w64-x86_64-gdb
   ```

4. **添加到系统PATH**
   - 将 `C:\msys64\mingw64\bin` 添加到系统环境变量PATH中

5. **编译项目**
   ```cmd
   cd 项目目录
   build_mingw.bat
   ```

## 方案三：在线编译 (临时方案)

如果您暂时无法安装编译器，我可以为您提供预编译的可执行文件。

## 验证安装

安装完成后，在命令提示符中运行以下命令验证：

**Visual Studio:**
```cmd
cl
```

**MinGW:**
```cmd
gcc --version
```

## 常见问题

1. **权限问题**: 请以管理员身份运行安装程序
2. **路径问题**: 确保编译器已添加到系统PATH
3. **版本兼容**: 推荐使用64位版本的编译器

选择其中一种方案安装后，就可以成功编译项目了！