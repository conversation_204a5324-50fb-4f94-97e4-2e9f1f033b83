@echo off
echo ========================================
echo    无模块DLL注入工具演示
echo ========================================
echo.

:: 检查是否已编译
if not exist "bin\Injector.exe" (
    echo [-] 未找到编译后的文件
    echo [!] 请先运行 build.bat 编译项目
    pause
    exit /b 1
)

echo [+] 启动测试目标程序...
start "TestTarget" bin\TestTarget.exe

echo [+] 等待目标程序启动...
timeout /t 3 /nobreak >nul

echo [+] 获取TestTarget进程ID...
for /f "tokens=2" %%i in ('tasklist /fi "imagename eq TestTarget.exe" /fo table /nh') do set PID=%%i

if "%PID%"=="" (
    echo [-] 未找到TestTarget进程
    echo [!] 请确保TestTarget.exe正在运行
    pause
    exit /b 1
)

echo [+] 找到TestTarget进程，PID: %PID%

echo.
echo [+] 执行DLL注入...
bin\Injector.exe TestTarget.exe bin\Payload.dll

if %errorlevel% neq 0 (
    echo [-] 注入失败
    pause
    exit /b 1
)

echo.
echo [+] 注入成功！现在可以使用Controller.exe进行内存操作
echo [+] 在新窗口中启动控制器...
start "Controller" bin\Controller.exe

echo.
echo ========================================
echo 演示说明:
echo 1. TestTarget.exe - 测试目标程序已启动
echo 2. Payload.dll - 已成功注入到目标进程
echo 3. Controller.exe - 内存操作控制器已启动
echo.
echo 你现在可以:
echo - 在Controller窗口中进行内存读写操作
echo - 在TestTarget窗口中查看程序状态
echo - 尝试修改TestTarget中的内存数据
echo ========================================
echo.
pause